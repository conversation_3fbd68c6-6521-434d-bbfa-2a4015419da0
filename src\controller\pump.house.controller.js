const { sql, query, queryWithParams } = require('../service/index.js')
const { isNumber } = require('../utils/index.js')
const { buildUUID, buildShortUUID } = require('../utils/uuid.js')

class PumpHouseController {
  async create(ctx, next) {
    try {
      const bodyData = ctx.request.body
      bodyData.PumpRoomNumber = buildUUID()
      bodyData.PumpRoomID = buildShortUUID('PumpRoomID')
      bodyData.SYSTEMID = buildShortUUID('SYSTEMID')
      bodyData.CurrentNode = 1

      const keys = Object.keys(bodyData).join(',')
      const values = Object.values(bodyData)
        .map((item) => {
          if (item === null) return 'NULL'
          if (isNumber(item)) return item
          return `'${item}'`
        })
        .join(',')
      const sentence = `INSERT INTO dbo.[SecondaryWaterProgressNew] (${keys}) VALUES (${values});`
      await query(sentence)
      const { recordsets } = await queryWithParams(`SELECT * FROM dbo.[SecondaryWaterProgressNew] WHERE PumpRoomNumber = @PumpRoomNumber`, {
        PumpRoomNumber: { type: sql.VarChar, value: bodyData.PumpRoomNumber }
      })
      ctx.body = { code: 200, msg: '创建成功', data: recordsets[0][0] }
    } catch (error) {
      ctx.body = { code: 400, msg: '创建失败', data: error.message }
    }
  }

  async update(ctx, next) {
    try {
      const bodyData = ctx.request.body
      const PumpRoomNumber = bodyData.PumpRoomNumber
      delete bodyData.PumpRoomNumber
      delete bodyData.Id
      delete bodyData.CurrentNode
      delete bodyData.UpdateTime
      bodyData.UpdatePerson = ctx.user.Name
      const data = Object.entries(bodyData)
      const sentence = `UPDATE SecondaryWaterProgressNew SET ${data.map((item) => `${item[0]} = ${item[1] === null || isNumber(item[1]) ? item[1] : `'${item[1]}'`}`).join(', ')} WHERE PumpRoomNumber = '${PumpRoomNumber}';`
      await query(sentence)
      ctx.body = { code: 200, message: '巡检成功' }
    } catch (error) {
      ctx.body = { code: 400, message: '修改失败', data: error.message }
    }
  }

  async gain(ctx, next) {
    try {
      const { PumpRoomNumber } = ctx.params
      const sentence = `SELECT * FROM SecondaryWaterProgressNew WHERE PumpRoomNumber = @PumpRoomNumber`
      const { recordsets } = await queryWithParams(sentence, {
        PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber }
      })
      ctx.body = { code: 200, message: '查询成功', data: recordsets[0][0] }
    } catch (error) {
      ctx.body = { code: 400, message: '查询失败', data: error.message }
    }
  }

  async list(ctx, next) {
    try {
      const { startTime, endTime, all, page, pageSize, pumpHouseName } = ctx.query

      // 分页参数处理
      const pageNum = page ? (Number(page) > 0 ? Number(page) : 1) : null
      const sizeNum = pageSize ? (Number(pageSize) > 0 ? Number(pageSize) : 10) : null

      // 构建基础字段
      const fields = all
        ? '*'
        : `PumpRoomNumber, BelongingArea, PumpHouseName, RemouldState,
          Gridding, BelongingStreet, PumpRoomControlledState, AccuratePosition,
          OperationManagementState, ProgressStatus, X, Y,
          ZoneCode, CurrentNode, UpdatePerson, UpdateTime, Batch`

      // 构建WHERE条件
      let whereClause = ''
      const params = {}
      const conditions = []

      if (startTime && endTime) {
        conditions.push('UpdateTime >= @startTime AND UpdateTime <= @endTime')
        params.startTime = { type: sql.DateTime, value: startTime }
        params.endTime = { type: sql.DateTime, value: endTime }
      }

      if (pumpHouseName) {
        conditions.push('PumpHouseName LIKE @pumpHouseName')
        params.pumpHouseName = { type: sql.VarChar, value: `%${pumpHouseName}%` }
      }

      if (conditions.length > 0) {
        whereClause = `WHERE ${conditions.join(' AND ')}`
      }

      // 如果有分页参数，执行分页查询
      if (pageNum && sizeNum) {
        // 查询总数
        const countSql = `SELECT COUNT(*) as total FROM SecondaryWaterProgressNew ${whereClause}`
        const { recordsets: countSets } = await queryWithParams(countSql, params)
        const total = countSets[0][0].total

        // 查询分页数据
        const offset = (pageNum - 1) * sizeNum
        const dataSql = `SELECT ${fields} FROM SecondaryWaterProgressNew ${whereClause} ORDER BY UpdateTime DESC OFFSET @offset ROWS FETCH NEXT @sizeNum ROWS ONLY`

        params.offset = { type: sql.Int, value: offset }
        params.sizeNum = { type: sql.Int, value: sizeNum }

        const { recordsets } = await queryWithParams(dataSql, params)

        // 计算分页信息
        const totalPages = Math.ceil(total / sizeNum)

        ctx.body = {
          code: 200,
          message: '查询成功',
          data: recordsets[0],
          pagination: {
            page: pageNum,
            pageSize: sizeNum,
            total,
            totalPages,
            hasNext: pageNum < totalPages,
            hasPrev: pageNum > 1
          }
        }
      } else {
        // 不分页，返回所有数据
        const dataSql = `SELECT ${fields} FROM SecondaryWaterProgressNew ${whereClause} ORDER BY UpdateTime DESC`
        const { recordsets } = await queryWithParams(dataSql, params)
        ctx.body = { code: 200, message: '查询成功', data: recordsets[0] }
      }
    } catch (error) {
      ctx.body = { code: 400, message: '查询失败', data: error.message }
    }
  }

  async wxlist(ctx, next) {
    try {
      const { all, page, pageSize, pumpHouseName, scope, LowZoneTheoreticalEndPressure, MidZoneTheoreticalEndPressure, HighZoneTheoreticalEndPressure } = ctx.query

      // 分页参数处理
      const pageNum = page ? (Number(page) > 0 ? Number(page) : 1) : null
      const sizeNum = pageSize ? (Number(pageSize) > 0 ? Number(pageSize) : 10) : null

      // 构建基础字段
      const fields = all
        ? '*'
        : `PumpRoomNumber, BelongingArea, PumpHouseName, RemouldState,
          Gridding, BelongingStreet, PumpRoomControlledState, AccuratePosition,
          OperationManagementState, ProgressStatus, X, Y,
          ZoneCode, CurrentNode, UpdatePerson, UpdateTime, Batch`

      // 构建WHERE条件
      let whereClause = ''
      const params = {}
      const conditions = []

      if (pumpHouseName) {
        conditions.push('PumpHouseName LIKE @pumpHouseName')
        params.pumpHouseName = { type: sql.VarChar, value: `%${pumpHouseName}%` }
      }

      // 数据划分筛选
      if (scope) {
        const [key, value] = scope.split(':')
        if (key === 'subject') {
          conditions.push('Gridding LIKE @Gridding')
          params.Gridding = { type: sql.VarChar, value: `%${value}%` }
        } else {
          conditions.push(`${key} = @${key}`)
          params[key] = { type: sql.VarChar, value: value }
        }
      }

      // 处理压力条件（使用OR逻辑）
      const pressureConditions = []
      if (LowZoneTheoreticalEndPressure) {
        pressureConditions.push('LowZoneTheoreticalEndPressure > @LowZoneTheoreticalEndPressure')
        params.LowZoneTheoreticalEndPressure = { type: sql.VarChar, value: LowZoneTheoreticalEndPressure }
      }
      if (MidZoneTheoreticalEndPressure) {
        pressureConditions.push('MidZoneTheoreticalEndPressure > @MidZoneTheoreticalEndPressure')
        params.MidZoneTheoreticalEndPressure = { type: sql.VarChar, value: MidZoneTheoreticalEndPressure }
      }
      if (HighZoneTheoreticalEndPressure) {
        pressureConditions.push('HighZoneTheoreticalEndPressure > @HighZoneTheoreticalEndPressure')
        params.HighZoneTheoreticalEndPressure = { type: sql.VarChar, value: HighZoneTheoreticalEndPressure }
      }

      // 如果有压力条件，将它们用OR连接后作为一个整体条件添加
      if (pressureConditions.length > 0) {
        conditions.push(`(${pressureConditions.join(' OR ')})`)
      }

      if (conditions.length > 0) {
        whereClause = `WHERE ${conditions.join(' AND ')}`
      }

      // 如果有分页参数，执行分页查询
      if (pageNum && sizeNum) {
        // 查询总数
        const countSql = `SELECT COUNT(*) as total FROM SecondaryWaterProgressNew ${whereClause}`
        const { recordsets: countSets } = await queryWithParams(countSql, params)
        const total = countSets[0][0].total

        // 查询分页数据
        const offset = (pageNum - 1) * sizeNum
        const dataSql = `SELECT ${fields} FROM SecondaryWaterProgressNew ${whereClause} ORDER BY UpdateTime DESC OFFSET @offset ROWS FETCH NEXT @sizeNum ROWS ONLY`

        params.offset = { type: sql.Int, value: offset }
        params.sizeNum = { type: sql.Int, value: sizeNum }

        const { recordsets } = await queryWithParams(dataSql, params)

        // 计算分页信息
        const totalPages = Math.ceil(total / sizeNum)

        ctx.body = {
          code: 200,
          message: '查询成功',
          data: recordsets[0],
          pagination: {
            page: pageNum,
            pageSize: sizeNum,
            total,
            totalPages,
            hasNext: pageNum < totalPages,
            hasPrev: pageNum > 1
          }
        }
      } else {
        // 不分页，返回所有数据
        const dataSql = `SELECT ${fields} FROM SecondaryWaterProgressNew ${whereClause} ORDER BY UpdateTime DESC`
        const { recordsets } = await queryWithParams(dataSql, params)
        ctx.body = { code: 200, message: '查询成功', data: recordsets[0] }
      }
    } catch (error) {
      ctx.body = { code: 400, message: '查询失败', data: error.message }
    }
  }

  async seek(ctx, next) {
    try {
      const { pumpHouseName } = ctx.query
      const sentence = `SELECT * FROM SecondaryWaterProgressNew WHERE PumpHouseName LIKE @pumpHouseName`
      const { recordsets } = await queryWithParams(sentence, {
        pumpHouseName: { type: sql.VarChar, value: `%${pumpHouseName}%` }
      })
      ctx.body = { code: 200, message: '查询成功', data: recordsets[0] }
    } catch (error) {
      ctx.body = { code: 400, message: '查询失败', data: error.message }
    }
  }

  async seekScope(ctx, next) {
    try {
      const { ZoneCode } = ctx.query
      const sentence = `SELECT * FROM SecondaryWaterProgressNew WHERE ZoneCode = @ZoneCode`
      const { recordsets } = await queryWithParams(sentence, {
        ZoneCode: { type: sql.VarChar, value: ZoneCode }
      })
      ctx.body = { code: 200, message: '查询成功', data: recordsets[0] }
    } catch (error) {
      ctx.body = { code: 400, message: '查询失败', data: error.message }
    }
  }
}

class PumpHouseNodeController {
  async create(ctx) {
    try {
      const { PumpRoomNumber, Node, CompletionTime } = ctx.request.body
      if (!PumpRoomNumber || !Node) return (ctx.body = { code: 400, msg: '参数缺失' })
      const { recordsets } = await queryWithParams(`SELECT * FROM SecondaryWaterProgressNode WHERE Node = @Node AND PumpRoomNumber = @PumpRoomNumber`, {
        Node: { type: sql.Int, value: Node },
        PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber }
      })
      if (recordsets[0].length) return (ctx.body = { code: 200, msg: '节点已存在' })
      const sentence = `INSERT INTO dbo.[SecondaryWaterProgressNode] (PumpRoomNumber,IsEnd,Node, CompletionTime, Remark, UpdatePerson)
      VALUES (@PumpRoomNumber,0,@Node,@CompletionTime,NULL,@UpdatePerson)`
      await queryWithParams(sentence, {
        PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber },
        Node: { type: sql.Int, value: Node },
        CompletionTime: { type: sql.DateTime, value: CompletionTime },
        UpdatePerson: { type: sql.VarChar, value: ctx.user.Name }
      })

      const { recordsets: res } = await queryWithParams(`SELECT MAX(Node) AS newestNode FROM SecondaryWaterProgressNode WHERE PumpRoomNumber = @PumpRoomNumber`, {
        PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber }
      })
      if (res[0][0].newestNode) {
        await queryWithParams(`UPDATE dbo.[SecondaryWaterProgressNew] SET CurrentNode = @CurrentNode WHERE PumpRoomNumber = @PumpRoomNumber`, {
          CurrentNode: { type: sql.Int, value: res[0][0].newestNode },
          PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber }
        })
      } else {
        await queryWithParams(`UPDATE dbo.[SecondaryWaterProgressNew] SET CurrentNode = @CurrentNode WHERE PumpRoomNumber = @PumpRoomNumber`, {
          CurrentNode: { type: sql.Int, value: 1 },
          PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber }
        })
      }
      ctx.body = { code: 200, message: '添加成功' }
    } catch (error) {
      ctx.body = { code: 400, message: '添加失败', data: error.message }
    }
  }
  async update(ctx) {
    try {
      const { PumpRoomNumber, IsEnd, Node, CompletionTime, Remark } = ctx.request.body
      if (!PumpRoomNumber || !Node || !CompletionTime) return (ctx.body = { code: 400, msg: '参数缺失' })
      const { recordsets } = await queryWithParams(`SELECT * FROM SecondaryWaterProgressNode WHERE Node = @Node AND PumpRoomNumber = @PumpRoomNumber`, {
        Node: { type: sql.Int, value: Node },
        PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber }
      })
      if (recordsets[0].length) {
        const sentence = `UPDATE dbo.[SecondaryWaterProgressNode] SET IsEnd = @IsEnd, CompletionTime = @CompletionTime, Remark = @Remark, UpdatePerson = @UpdatePerson WHERE Node = @Node AND PumpRoomNumber = @PumpRoomNumber`
        await queryWithParams(sentence, {
          IsEnd: { type: sql.Bit, value: IsEnd },
          CompletionTime: { type: sql.DateTime, value: CompletionTime },
          Remark: { type: sql.VarChar, value: Remark },
          UpdatePerson: { type: sql.VarChar, value: ctx.user.Name },
          Node: { type: sql.Int, value: Node },
          PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber }
        })
      } else {
        const sentence = `INSERT INTO dbo.[SecondaryWaterProgressNode] (PumpRoomNumber,IsEnd,Node, CompletionTime, Remark, UpdatePerson)
        VALUES (@PumpRoomNumber,@IsEnd,@Node,@CompletionTime,@Remark,@UpdatePerson)`
        await queryWithParams(sentence, {
          PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber },
          IsEnd: { type: sql.Bit, value: IsEnd },
          Node: { type: sql.Int, value: Node },
          CompletionTime: { type: sql.DateTime, value: CompletionTime },
          Remark: { type: sql.VarChar, value: Remark },
          UpdatePerson: { type: sql.VarChar, value: ctx.user.Name }
        })
      }

      const { recordsets: res } = await queryWithParams(`SELECT MAX(Node) AS newestNode FROM SecondaryWaterProgressNode WHERE PumpRoomNumber = @PumpRoomNumber AND IsEnd = 1`, {
        PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber }
      })
      if (res[0][0].newestNode) {
        await queryWithParams(`UPDATE dbo.[SecondaryWaterProgressNew] SET CurrentNode = @CurrentNode, UpdatePerson = @UpdatePerson WHERE PumpRoomNumber = @PumpRoomNumber`, {
          CurrentNode: { type: sql.Int, value: res[0][0].newestNode },
          UpdatePerson: { type: sql.VarChar, value: ctx.user.Name },
          PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber }
        })
      } else {
        await queryWithParams(`UPDATE dbo.[SecondaryWaterProgressNew] SET CurrentNode = @CurrentNode WHERE PumpRoomNumber = @PumpRoomNumber`, {
          CurrentNode: { type: sql.Int, value: 1 },
          PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber }
        })
      }

      ctx.body = { code: 200, message: '更新成功' }
    } catch (error) {
      ctx.body = { code: 400, message: '更新失败', data: error.message }
    }
  }
  async gain(ctx) {
    try {
      const { PumpRoomNumber } = ctx.params
      const { Node } = ctx.query
      const sentence = `SELECT
        n.*,
        (SELECT * FROM SecondaryWaterProgressNodeFiles AS f WHERE f.PumpRoomNumber = n.PumpRoomNumber AND f.Node = n.Node FOR JSON PATH) AS Files
        FROM SecondaryWaterProgressNode AS n WHERE n.PumpRoomNumber = @PumpRoomNumber AND n.Node = @Node`
      const { recordsets } = await queryWithParams(sentence, {
        PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber },
        Node: { type: sql.Int, value: Node }
      })
      ctx.body = { code: 200, message: '获取成功', data: recordsets[0][0] ?? null }
    } catch (error) {
      ctx.body = { code: 400, message: '获取失败', data: error.message }
    }
  }
  async list(ctx) {
    try {
      const { PumpRoomNumber } = ctx.params
      const sentence = `SELECT
        n.*,
        (SELECT * FROM SecondaryWaterProgressNodeFiles AS f WHERE f.PumpRoomNumber = n.PumpRoomNumber AND f.Node = n.Node FOR JSON PATH) AS Files
        FROM SecondaryWaterProgressNode AS n WHERE n.PumpRoomNumber = @PumpRoomNumber ORDER BY Node ASC`
      const { recordsets } = await queryWithParams(sentence, {
        PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber }
      })
      ctx.body = { code: 200, message: '获取成功', data: recordsets[0] }
    } catch (error) {
      ctx.body = { code: 400, message: '获取失败', data: error.message }
    }
  }
}

// 节点文件管理
class PumpHouseNodeFileController {
  async create(ctx, next) {
    try {
      const { PumpRoomNumber, Node, FileType, Path } = ctx.request.body
      if (!PumpRoomNumber || !Node || !FileType || !Path) return (ctx.body = { code: 400, msg: '参数缺失' })
      const sentence = `INSERT INTO SecondaryWaterProgressNodeFiles (PumpRoomNumber, Node, FileType, Path, UploadPerson) VALUES (@PumpRoomNumber, @Node, @FileType, @Path, @UploadPerson)`
      await queryWithParams(sentence, {
        PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber },
        Node: { type: sql.Int, value: Node },
        FileType: { type: sql.VarChar, value: FileType },
        Path: { type: sql.VarChar, value: Path },
        UploadPerson: { type: sql.VarChar, value: ctx.user.Name }
      })
      ctx.body = { code: 200, message: '添加成功' }
    } catch (error) {
      ctx.body = { code: 400, message: '添加失败', data: error.message }
    }
  }
  async update(ctx, next) {
    try {
      const { PumpRoomNumber, Node, FileType, Path } = ctx.request.body
      if (!PumpRoomNumber || !Node || !FileType || !Path) return (ctx.body = { code: 400, msg: '参数缺失' })
      const { recordsets } = await queryWithParams(`SELECT * FROM SecondaryWaterProgressNodeFiles WHERE PumpRoomNumber = @PumpRoomNumber AND Node = @Node AND FileType = @FileType`, {
        PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber },
        Node: { type: sql.Int, value: Node },
        FileType: { type: sql.VarChar, value: FileType }
      })
      if (!recordsets[0].length) return (ctx.body = { code: 400, msg: '未找到该条记录' })
      const sentence = `UPDATE SecondaryWaterProgressNodeFiles SET Path = @Path, UploadPerson = @UploadPerson WHERE PumpRoomNumber = @PumpRoomNumber AND Node = @Node AND FileType = @FileType`
      await queryWithParams(sentence, {
        Path: { type: sql.VarChar, value: Path },
        UploadPerson: { type: sql.VarChar, value: ctx.user.Name },
        PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber },
        Node: { type: sql.Int, value: Node },
        FileType: { type: sql.VarChar, value: FileType }
      })
    } catch (error) {
      ctx.body = { code: 400, message: '修改失败', data: error.message }
    }
  }
}

// 二供巡检任务
class PumpHouseTaskController {
  // 创建巡检任务
  async create(ctx) {
    const { TaskScope = '', TaskContent, TaskEndTime, TaskRemark } = ctx.request.body
    const sentence = `INSERT INTO dbo.[SecondaryWaterTask] (TaskScope,TaskContent,TaskEndTime,TaskRemark,Initiator) VALUES (@TaskScope,@TaskContent,@TaskEndTime,@TaskRemark,@Initiator);`
    await queryWithParams(sentence, {
      TaskScope: { type: sql.VarChar, value: TaskScope },
      TaskContent: { type: sql.VarChar, value: TaskContent },
      TaskEndTime: { type: sql.DateTime, value: TaskEndTime },
      TaskRemark: { type: sql.VarChar, value: TaskRemark },
      Initiator: { type: sql.Int, value: ctx.user.id }
    })
    ctx.body = { code: 200, msg: '创建成功' }
  }

  // 发布者查询任务列表
  async list(ctx) {
    try {
      const { page = 1, pageSize = 10, startTime, endTime, initiator } = ctx.query

      // 分页参数处理
      const pageNum = page ? (Number(page) > 0 ? Number(page) : 1) : null
      const sizeNum = pageSize ? (Number(pageSize) > 0 ? Number(pageSize) : 10) : null

      // 构建WHERE条件
      let whereClause = ''
      const params = {}
      const conditions = []

      // 时间范围筛选
      if (startTime && endTime) {
        conditions.push('TaskEndTime >= @startTime AND TaskEndTime <= @endTime')
        params.startTime = { type: sql.DateTime, value: startTime }
        params.endTime = { type: sql.DateTime, value: endTime }
      }

      // 发起人筛选
      if (initiator) {
        conditions.push('Initiator = @initiator')
        params.initiator = { type: sql.Int, value: Number(initiator) }
      }

      if (conditions.length > 0) {
        whereClause = `WHERE ${conditions.join(' AND ')}`
      }

      // 如果有分页参数，执行分页查询
      if (pageNum && sizeNum) {
        // 查询总数
        const countSql = `SELECT COUNT(*) as total FROM dbo.[SecondaryWaterTask] ${whereClause}`
        const { recordsets: countSets } = await queryWithParams(countSql, params)
        const total = countSets[0][0].total

        // 查询分页数据
        const offset = (pageNum - 1) * sizeNum
        const dataSql = `SELECT * FROM dbo.[SecondaryWaterTask] ${whereClause} ORDER BY CreateTime DESC OFFSET @offset ROWS FETCH NEXT @sizeNum ROWS ONLY`

        params.offset = { type: sql.Int, value: offset }
        params.sizeNum = { type: sql.Int, value: sizeNum }

        const { recordsets } = await queryWithParams(dataSql, params)

        // 计算分页信息
        const totalPages = Math.ceil(total / sizeNum)

        ctx.body = {
          code: 200,
          message: '查询成功',
          data: recordsets[0],
          pagination: {
            page: pageNum,
            pageSize: sizeNum,
            total,
            totalPages,
            hasNext: pageNum < totalPages,
            hasPrev: pageNum > 1
          }
        }
      } else {
        // 不分页，返回所有数据
        const dataSql = `SELECT * FROM dbo.[SecondaryWaterTask] ${whereClause} ORDER BY CreateTime DESC`
        const { recordsets } = await queryWithParams(dataSql, params)
        ctx.body = { code: 200, message: '查询成功', data: recordsets[0] }
      }
    } catch (error) {
      ctx.body = { code: 400, message: '查询失败', data: error.message }
    }
  }

  // 查询待巡查的列表
  async pendingList(ctx) {
    try {
      const { end = '0' } = ctx.query
      const userId = ctx.user.id

      // 构建时间条件
      const timeCondition = end == '0' ? 'TaskEndTime >= GETDATE() AND TaskEndTime <= DATEADD(DAY, 7, GETDATE())' : 'TaskEndTime <= GETDATE()'

      // 一次性查询所有任务及其进度信息的复杂SQL
      const optimizedQuery = `
        WITH UserPumpRooms AS (
          -- 获取用户负责的所有泵房
          SELECT PumpRoomNumber
          FROM dbo.[SecondaryWaterPatrol]
          WHERE PrimaryResponsiblePerson = @userId OR SecondaryResponsiblePerson = @userId
        ),
        TaskProgress AS (
          -- 计算每个任务的完成进度
          SELECT
            t.TaskID,
            COUNT(DISTINCT upr.PumpRoomNumber) AS totalPumpRooms,
            COUNT(DISTINCT CASE
              WHEN r.PumpRoomNumber IS NOT NULL THEN r.PumpRoomNumber
              ELSE NULL
            END) AS completedPumpRooms
          FROM dbo.[SecondaryWaterTask] t
          CROSS JOIN UserPumpRooms upr
          LEFT JOIN dbo.[SecondaryWaterRecord] r ON r.TaskID = t.TaskID AND r.PumpRoomNumber = upr.PumpRoomNumber
          WHERE ${timeCondition}
          GROUP BY t.TaskID
        )
        SELECT
          t.*,
          ISNULL(tp.totalPumpRooms, 0) AS totalPumpRooms,
          ISNULL(tp.completedPumpRooms, 0) AS completedPumpRooms,
          CASE
            WHEN ISNULL(tp.totalPumpRooms, 0) > 0
            THEN ROUND((CAST(ISNULL(tp.completedPumpRooms, 0) AS FLOAT) / CAST(tp.totalPumpRooms AS FLOAT)) * 100, 0)
            ELSE 0
          END AS progressPercentage
        FROM dbo.[SecondaryWaterTask] t
        LEFT JOIN TaskProgress tp ON t.TaskID = tp.TaskID
        WHERE ${timeCondition}
        ORDER BY t.TaskEndTime ASC
      `
      // 同时查询用户负责的泵房总数
      const inspectionPointQuery = `
        SELECT COUNT(*) AS count
        FROM dbo.[SecondaryWaterPatrol]
        WHERE PrimaryResponsiblePerson = @userId OR SecondaryResponsiblePerson = @userId
      `

      // 并行执行两个查询
      const [tasksResult, inspectionPointResult] = await Promise.all([queryWithParams(optimizedQuery, { userId: { type: sql.Int, value: userId } }), queryWithParams(inspectionPointQuery, { userId: { type: sql.Int, value: userId } })])

      const tasks = tasksResult.recordsets[0]
      const inspectionPoint = inspectionPointResult.recordsets[0][0].count

      // 转换数据格式，将SQL查询结果转换为前端需要的格式
      const tasksWithProgress = tasks.map((task) => {
        const { totalPumpRooms, completedPumpRooms, progressPercentage, ...taskData } = task
        return {
          ...taskData,
          inspectionProgress: {
            totalPumpRooms: totalPumpRooms || 0,
            completedPumpRooms: completedPumpRooms || 0,
            progressPercentage: progressPercentage || 0
          }
        }
      })

      ctx.body = {
        code: 200,
        message: '查询成功',
        data: tasksWithProgress
      }
    } catch (error) {
      ctx.body = { code: 400, message: '查询失败', data: error.message }
    }
  }
}

const pumpHouse = new PumpHouseController()
const node = new PumpHouseNodeController()
const nodeFile = new PumpHouseNodeFileController()
const task = new PumpHouseTaskController()

module.exports = { pumpHouse, node, nodeFile, task }
