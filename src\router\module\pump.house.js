const Router = require('@koa/router')
const controller = require('../../controller/pump.house.controller.js')
const router = new Router({ prefix: '/pumpHouse' })

// 泵房管理
router.post('/', controller.pumpHouse.create)
router.put('/', controller.pumpHouse.update)
router.get('/detail/:PumpRoomNumber', controller.pumpHouse.gain)
router.get('/list', controller.pumpHouse.list)
router.get('/wxlist', controller.pumpHouse.wxlist)
router.get('/seek', controller.pumpHouse.seek)
router.get('/seekScope', controller.pumpHouse.seekScope)

// 节点信息管理
router.post('/node', controller.node.create)
router.put('/node', controller.node.update)
router.get('/node/:PumpRoomNumber', controller.node.gain)
router.get('/node/list/:PumpRoomNumber', controller.node.list)

// 节点文件管理
router.post('/nodeFile', controller.nodeFile.create)
router.put('/nodeFile', controller.nodeFile.update)

// 巡检任务
router.post('/task', controller.task.create)
router.get('/task/list', controller.task.list)
router.get('/task/pendingList', controller.task.pendingList)
router.get('/task/inspectionPoint', controller.task.inspectionPoint)
// router.get('/task/list/:PumpRoomNumber', controller.node.list)

module.exports = router
